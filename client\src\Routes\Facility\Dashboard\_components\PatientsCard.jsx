import Woman<PERSON>an<PERSON><PERSON><PERSON><PERSON> from './WomanManPieChart';

export default function PatientsCard({ data }) {
  if (!data) {
    return (
      <div className="flex flex-col bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="px-6 pt-6 pb-2">
          <h2 className="text-xl font-semibold text-gray-800">Patients</h2>
        </div>
        <div className="px-6 py-8 flex items-center justify-center">
          <p className="text-gray-500">Loading patient data...</p>
        </div>
      </div>
    );
  }
console.log(data)
  return (
    <div className="flex flex-col justify-between bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
      <div className="px-6 pt-6 pb-2">
        <h2 className="text-xl font-semibold text-gray-800">Patients</h2>
      </div>

      <div className="flex justify-between items-center px-6 py-2">
      
         <div className="flex items-center gap-4">
          <div className="p-3 bg-blue-50 rounded-full">
            <i className="fas fa-user-injured text-blue-600 text-3xl"></i>
          </div>
          <p className="text-4xl font-bold text-gray-800">{data.total || 0}</p>
        </div>

        <div className="w-[100px] h-[100px] transform hover:scale-105 transition-transform">
          <WomanManPieChart data={data.genderData} />
        </div>
      </div>

      <div className="flex items-center justify-between border-t border-gray-100 bg-gray-50 px-6 py-6">
        <p className="text-sm text-gray-600 font-medium">{data.last7Days || 0} in last 7 days</p>
        <div className="flex gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">F</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-pink-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">M</span>
          </div>
        </div>
      </div>
    </div>
  );
}
