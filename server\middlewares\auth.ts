import { Request, Response, NextFunction } from "express";
import { validateUserJWT } from "../utils/jwt";

export const authMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // For demonstration: assign a dummy user. Replace this with your actual auth logic.
    if(!req.headers.authorization || req.headers.authorization.trim() == ""){
      throw new Error("Unauthorized");
    }
    const user = validateUserJWT(req.headers.authorization );

    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ message: "Unauthorized" });
  }
};
