export default function FacilityStaffCard({ data }) {
  if (!data) {
    return (
      <div className="flex flex-col bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="px-6 pt-6 pb-2">
          <h2 className="text-xl font-semibold text-gray-800">Facility Staff</h2>
        </div>
        <div className="px-6 py-8 flex items-center justify-center">
          <p className="text-gray-500">Loading staff data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-between bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
      <div className="px-6 pt-6 pb-2">
        <h2 className="text-xl font-semibold text-gray-800">Facility Staff</h2>
      </div>

      <div className="flex justify-between items-center px-6 py-2">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-blue-50 rounded-full">
            <i className="fas fa-users text-blue-600 text-3xl"></i>
          </div>
          <p className="text-4xl font-bold text-gray-800">{data.totalWorkers || 0}</p>
        </div>
      </div>

      {/* Staff Composition Bar Graph - Kept original but with matched styling */}
      <div className="px-6 py-4">
        <div className='h-[30px] w-full bg-amber-100 flex rounded-[20px] overflow-hidden hover:transform-[scale(1.03)]'>
          <div 
            className="admin h-full bg-blue-600 transition-all duration-500 " 
            style={{ width: `${(data.admins / data.totalWorkers) * 100}%` }}
          ></div>
          <div 
            className="doc h-full bg-pink-600 transition-all duration-500" 
            style={{ width: `${(data.doctors / data.totalWorkers) * 100}%` }}
          ></div>
        </div>
      </div>

      <div className="flex items-center justify-between border-t border-gray-100 bg-gray-50 px-6 py-6">
        <div className="flex  gap-1">
          <p className="text-sm text-gray-600 font-medium">Doctors: {data.doctors || 0} | </p> 
          <p className="text-sm text-gray-600 font-medium">Admins: {data.admins || 0} | </p> 
          <p className="text-sm text-gray-600 font-medium">total: {data.admins + data.doctors || 0}</p>
        </div>
        <div className="flex gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">Admins</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-pink-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-600">Doctors</span>
          </div>
        </div>
      </div>
    </div>
  );
}
