// Data transformation functions
export function transformPatientsData(patientsData) {
  if (!patientsData || !Array.isArray(patientsData) || patientsData.length === 0) {
    return null;
  }

  const data = patientsData[0];
  const genderCounts = data.countByGender || [];
  const last7DaysData = data.totalLast7Days || [];

  // Calculate total patients and gender breakdown
  const maleCount = genderCounts.find(g => g.gender === 'M')?.count || 0;
  const femaleCount = genderCounts.find(g => g.gender === 'F')?.count || 0;
  const total = maleCount + femaleCount;
  const last7Days = last7DaysData.length > 0 ? last7DaysData[0].count : 0;

  return {
    total,
    male: maleCount,
    female: femaleCount,
    last7Days,
    genderData: [
      { name: 'Female', value: femaleCount },
      { name: 'Male', value: maleCount }
    ]
  };
}

export function transformStaffData(staffData) {
  if (!staffData || !Array.isArray(staffData)) {
    return null;
  }

  const adminCount = staffData.find(s => s.type === 'admin')?.count || 0;
  const doctorCount = staffData.find(s => s.type === 'doctor')?.count || 0;
  const totalWorkers = adminCount + doctorCount;

  return {
    totalWorkers,
    admins: adminCount,
    doctors: doctorCount
  };
}

export function transformMedicalRecordsData(medicalRecordsData) {
  if (!medicalRecordsData || !Array.isArray(medicalRecordsData)) {
    return null;
  }

  // Calculate total records for last 7 days (simplified - using total for now)
  const totalRecords = medicalRecordsData.reduce((sum, record) => sum + record.count, 0);

  return {
    last7Days: totalRecords,
    monthlyData: medicalRecordsData
  };
}
