import { <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';

export default function WomanManPie<PERSON>hart({ data }) {
  const COLORS = ['#3b82f6', '#ec4899'];

  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <div className="w-full h-full bg-gray-100 rounded-full flex items-center justify-center">
        <span className="text-gray-400 text-xs">No data</span>
      </div>
    );
  }

  return (
    <PieChart width={100} height={100}>
      <Pie
        data={data}
        cx="50%"
        cy="50%"
        innerRadius={25}
        outerRadius={40}
        paddingAngle={2}
        dataKey="value"
        animationBegin={100}
        animationDuration={1000}
      >
        {data.map((_, index) => (
          <Cell
            key={`cell-${index}`}
            fill={COLORS[index % COLORS.length]}
            stroke="transparent"
            
        
          />
        ))}
      </Pie>
    </PieChart>
  );
}
