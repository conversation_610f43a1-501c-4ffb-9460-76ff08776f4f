import { Router } from "express";
import { User } from "../../Models/User";
import { generateUserJWT } from "../../utils/jwt";
import { hashPassword } from "../../utils/passwordHash";
import { authMiddleware } from "../../middlewares/auth";
const router = Router();
router.post("/", async (req, res) => {
  const { email, password } = req.body;
  if (!email || !password) {
    res.status(400).json({ message: "Please provide email and password" });
    return;
  }
  const response = await User.findOne({
    email,
    password: hashPassword(password),
  })
  .populate('profiles.hospital', 'name type')
  .lean();

  if (!response) {
    res.status(401).json({ message: "Invalid email or password" });
    return;
  }

  // Add hospital name to profiles while keeping existing schema
  const profilesWithHospitalNames = response.profiles.map((profile: any) => ({
    ...profile,
    hospitalName: profile.hospital?.name || null,
    hospitalType: profile.hospital?.type || null,
  }));

  const responseData = {
    ...response,
    profiles: profilesWithHospitalNames,
  };

  res.json({
    data: responseData,
    tokens: response.profiles.map((p) =>
      generateUserJWT({
        id: response._id.toString(),
        role: p.type,
        email: response.email,
      })
    ),
  });
});
router.get("/token",authMiddleware, async (req, res) => {


  const response = await User.findById(req.user.id)
  .lean();
  if (!response) {
    res.status(401).json({ message: "deleted user" });
    return;
  }



  const responseData = {
    ...response,
    role: req.user.role,
  };

  res.json({
    data: responseData,
    token: req.headers.authorization
  });

})
export default router;
