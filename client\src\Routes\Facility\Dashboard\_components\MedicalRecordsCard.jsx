import MedicalRecordsChartCard from './MedicalRecordsChartCard';

const MonthNumberToName = [
  "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
];

export default function MedicalRecordsCard({ data }) {
  if (!data) {
    return (
      <div className="flex flex-col bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="px-6 pt-6 pb-2">
          <h2 className="text-xl font-semibold text-gray-800">Medical Records</h2>
        </div>
        <div className="px-6 py-8 flex items-center justify-center">
          <p className="text-gray-500">Loading medical records data...</p>
        </div>
      </div>
    );
  }

  // Transform monthly data
  data.monthlyData = MonthNumberToName.map((item, index) => ({
    name: item,
    records: data.monthlyData.find((item) => item.month === index + 1)?.count || 0
  }));

  return (
    <div className="flex flex-col bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
      <div className="px-6 pt-6 pb-2">
        <h2 className="text-xl font-semibold text-gray-800">Medical Records</h2>
      </div>

      <div className="py-4 flex justify-center">
        <MedicalRecordsChartCard data={data.monthlyData} />
      </div>

      <div className="flex items-center justify-between border-t border-gray-100 bg-gray-50 px-6 py-4">
        <p className="text-sm text-gray-600 font-medium">Monthly records overview</p>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium text-gray-600">Records</span>
        </div>
      </div>
    </div>
  );
}
