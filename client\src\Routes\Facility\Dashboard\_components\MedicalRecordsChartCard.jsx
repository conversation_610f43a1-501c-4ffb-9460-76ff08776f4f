import { <PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tooltip, Line } from 'recharts';

export default function MedicalRecordsChartCard({ data }) {
  if (!data) {
    return (
      <div className="h-64 bg-gray-100 rounded flex items-center justify-center">
        <p className="text-gray-500">Loading medical records data...</p>
      </div>
    );
  }

  return (
    <div className="w-full flex justify-center">
      <LineChart 
        width={500}
        height={250}
        data={data}
        margin={{
          top: 10,
          right: 30,
          left: 0,
          bottom: 0,
        }}
        
           
      >
        <CartesianGrid  strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="name" 
          tick={{ fill: '#6b7280' }}
          axisLine={{ stroke: '#e5e7eb' }}
        />
        <YAxis 
          tick={{ fill: '#6b7280' }}
          axisLine={{ stroke: '#e5e7eb' }}
        />
        <Tooltip 
          contentStyle={{
            backgroundColor: '#ffffff',
            borderColor: '#e5e7eb',
            borderRadius: '0.5rem',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}
        />
        <Line 
          connectNulls 
          type="monotone" 
          dataKey="records" 
          stroke="#ad46ff" 
          strokeWidth={2} 
          animationEasing='ease-in-out'
          isAnimationActive={true}
      animationBegin={0}
      animationDuration={1500} // Adjust duration (e.g., 1000 for 1s)
      animateNewValues={true} // Helps if data updates
          dot={{ r: 4, fill: '#ad46ff' }}
          activeDot={{ r: 6, stroke: '#ad46ff', strokeWidth: 2, fill: '#ffffff' }}
        />
      </LineChart>
    </div>
  );
}
