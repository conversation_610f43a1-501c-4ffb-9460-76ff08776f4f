import { formatDate } from '../../../../utils/dateFormater';

export default function FacilityDataCard({ data, facilityManager }) {
  if (!data) {
    return (
      <div className="flex flex-col bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="px-6 pt-6 pb-2">
          <h2 className="text-xl font-semibold text-gray-800">Facility Data</h2>
        </div>
        <div className="px-6 py-8 flex items-center justify-center">
          <p className="text-gray-500">Loading facility data...</p>
        </div>
      </div>
    );
  }

  return (
  <div className="bg-white flex flex-col justify-between rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
  <div className="px-6 pt-6 pb-2">
    <h2 className="text-xl font-semibold text-gray-800">Facility Information</h2>
  </div>

  <div className="flex flex-col md:flex-row">
    {/* Facility Details - Left Side */}
    <div className="px-6 py-4 space-y-4 md:w-1/2">
      <div className="flex items-center gap-4">
        <div className="p-3 bg-green-50 rounded-full">
          <i className="fas fa-hospital text-green-600 text-3xl"></i>
        </div>
        <div>
          <h3 className="text-lg font-bold text-gray-800">{data.name || 'N/A'}</h3>
          <p className="text-sm text-gray-500">{data.type || 'Facility type not available'}</p>
        </div>
      </div>

      <div className="space-y-3 text-sm">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
            <i className="fas fa-map-marker-alt text-gray-500 text-sm"></i>
          </div>
          <span className="text-gray-600">{data.address || 'Address not available'}</span>
        </div>
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
            <i className="fas fa-phone text-gray-500 text-sm"></i>
          </div>
          <span className="text-gray-600">{data.phone || 'Phone not available'}</span>
        </div>
        {data.createdAt && (
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <i className="fas fa-calendar text-gray-500 text-sm"></i>
            </div>
            <span className="text-gray-600">
              {formatDate(data.createdAt)}
            </span>
          </div>
        )}
      </div>
    </div>

    {/* Facility Manager Details - Right Side */}
    {facilityManager && (
      <div className="px-6 py-4 space-y-4 md:w-1/2">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-blue-50 rounded-full">
            <i className="fas fa-user-tie text-blue-600 text-3xl"></i>
          </div>
          <div>
            <h3 className="text-lg font-bold text-gray-800 flex items-center">
              {facilityManager.name.join(" ") || 'N/A'}
              {(() => {
                const genderData = {
                  M: { icon: "mars", color: "blue-600", label: "Male" },
                  F: { icon: "venus", color: "pink-600", label: "Female" },
                  default: { icon: "genderless", color: "gray-500", label: "Not specified" },
                };
                
                const { icon, color } = facilityManager.gender
                  ? genderData[facilityManager.gender] || genderData.default
                  : genderData.default;
                
                return (
                  <i className={`fas fa-${icon} text-${color} ml-2 text-lg`}></i>
                );
              })()}
            </h3>
            <p className="text-sm text-gray-500">Facility Manager</p>
          </div>
        </div>

        <div className="space-y-3 text-sm">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <i className="fa-solid fa-envelope text-gray-500 text-sm aspect-square"></i>
            </div>
            <span className="text-gray-600 truncate" title={facilityManager.email || 'Address not available'}>{facilityManager.email || 'Address not available'}</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <i className="fas fa-phone text-gray-500 text-sm"></i>
            </div>
            <span className="text-gray-600">{facilityManager.phone || 'Phone not available'}</span>
          </div>
          {facilityManager.BirthDay && (
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <i className="fas fa-calendar text-gray-500 text-sm"></i>
              </div>
              <span className="text-gray-600">
                {formatDate(facilityManager.BirthDay)}
              </span>
            </div>
          )}
        </div>
      </div>
    )}
  </div>

  {/* Legend Section - Added similar to Facility Staff card */}
  <div className="flex items-center justify-between border-t border-gray-100 bg-gray-50 px-6 py-3">
    <div className="flex flex-wrap gap-4">
     
    
      <div className="flex items-center gap-2">
        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
          <i className="fas fa-map-marker-alt text-gray-500 text-sm"></i>
        </div>
        <span className="text-sm font-medium text-gray-600">Location</span>
      </div>
      <div className="flex items-center gap-2">
        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
          <i className="fas fa-phone text-gray-500 text-sm"></i>
        </div>
        <span className="text-sm font-medium text-gray-600">Contact</span>
      </div> <div className="flex items-center gap-2">
        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <i className="fas fa-calendar text-gray-500 text-sm"></i>
              </div>
        <span className="text-sm font-medium text-gray-600">Birth - Creation date</span>
      </div>
    </div>
  </div>
</div>
  );
}
