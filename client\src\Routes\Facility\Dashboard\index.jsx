
import FacilityStaffCard from './_components/FacilityStaffCard';
import FacilityDataCard from './_components/FacilityDataCard';
import PatientsCard from './_components/PatientsCard';
import MedicalRecordsCard from './_components/MedicalRecordsCard';
import {
  transformPatientsData,
  transformStaffData,
  transformMedicalRecordsData
} from './_components/dataTransformers';
import { useFacilityOwnerDahsboardFetcher } from '../../../utils/costumeHook';
export default function FacilityDashboard() {
  // Fetch dashboard data
  const { dashboardData, error } = useFacilityOwnerDahsboardFetcher();
  // Transform API data for components
  const transformedData = dashboardData ? {
    patients: transformPatientsData(dashboardData.patients),
    staff: transformStaffData(dashboardData.staff),
    facility: dashboardData.facility,
    facilityManager: dashboardData.facilityManager,
    medicalRecords: transformMedicalRecordsData(dashboardData.medicalRecords)
  } : null;

  if (error) {
    return (
      <div className="p-6 max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">Facility Dashboard</h1>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <p className="text-red-600 font-medium">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 w-full px-10">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">Facility Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <PatientsCard data={transformedData?.patients} />
        <FacilityStaffCard data={transformedData?.staff} />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FacilityDataCard data={transformedData?.facility} facilityManager={transformedData?.facilityManager} />
        <MedicalRecordsCard data={transformedData?.medicalRecords} />
      </div>
    </div>
  );
}
